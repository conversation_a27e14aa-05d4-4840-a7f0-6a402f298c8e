/*
 * ESPRSSIF MIT License
 *
 * Copyright (c) 2015 <ESPRESSIF SYSTEMS (SHANGHAI) PTE LTD>
 *
 * Permission is hereby granted for use on ESPRESSIF SYSTEMS ESP8266 only, in which case,
 * it is free of charge, to any person obtaining a copy of this software and associated
 * documentation files (the "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the Software is furnished
 * to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all copies or
 * substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
 * FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
 * COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WH<PERSON>HER
 * IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
 * CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 *
 */
#include "ets_sys.h"
#include "os_type.h"
#include "osapi.h"
#include "spi_test.h"
#include "at_custom.h"
#include "user_interface.h"
#include "driver/uart.h"
#include "driver/spi_interface.h"
#include "espconn.h"
#include "mem.h"
#include "user_json.h"
#include "user_devicefind.h"
#include "eagle_soc.h"
#include "spi_flash.h"

LOCAL os_timer_t test_timer;
struct espconn PhoneConn;
esp_udp PhoneConnUdp;
LOCAL struct espconn user_udp_espconn;
LOCAL struct espconn ptrespconn;
const char *ESP8266_MSG = "I'm ESP8266 ";
const char *device_find_request = "Are You ESP8266 Device?";

const char *device_find_response_ok = "Yes,I'm ESP8266!";

uint8 udpTx_buf0[8292];
uint8 *udpTx_buf_pionter;
char udpRx_buf[32];
uint8 udpTxCmd_buf[32];
uint8 udpDataNeedSend = 0;
uint16 udpWait2SendBytes = 0;
uint8 tst_udpCnt0 = 0;
uint8 tst_udpCnt1 = 0;
#define UDP_LENGTH 1448 // UDP 一帧数据长度1406字节

#define SEC 124           //读写的扇区（Sector）号
#define SEC_OFFSET 0//扇区内偏移量（必须是4的倍数）
#define FLASH_LEN 8//以读写8*4字节的数据为例
uint8 flash_write_data[FLASH_LEN*4];
uint8 flash_read_data[FLASH_LEN*4];
uint8 ssid_buf[15] = "PREMAT3#null";
uint8 password_buf[15] = "12345678";
uint8 debugModeOpen = 0;
uint32 version = 20190117;
void saveData2Flash() {
	uint8 i = 0;
	uint8 j = 0;
//	os_printf("flash start");
	for(i = 0; i < 15; i++) {
		flash_write_data[j++] = ssid_buf[i];
	}
	for(i = 0; i < 15; i++) {
		flash_write_data[j++] = password_buf[i];
	}

	//写入数据
	spi_flash_erase_sector(SEC);
	spi_flash_write(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_write_data, FLASH_LEN*4); 
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4); 
	for(i=0;i<32;i++){
	os_printf("flash[%d]%c ", i,flash_read_data[i]);
	}
}
uint8 readFlash_SsidPassword() {
	uint8 i = 0;
	uint8 j = 0;
	spi_flash_read(SEC*4*1024+SEC_OFFSET, (uint32 *)&flash_read_data, FLASH_LEN*4);
	for(i = 0; i <15; i++) {
		ssid_buf[i] = flash_read_data[j++];
//		os_printf("s%d",ssid_buf[i]);
	}
	for(i = 0; i <15; i++) {
		password_buf[i] = flash_read_data[j++];
//		os_printf("p%d",password_buf[i]);
	}
	if((ssid_buf[0] != 'P') || (ssid_buf[1] != 'R')) {
//		os_printf("return 1 ssid_buf[0]%d ",ssid_buf[0]);
		return 0x01;
	}
//	os_printf("return 0 ssid_buf[0]%d ",ssid_buf[0]);
	return 0;
}

uint32_t udpRxBuf0[8] = {0};
uint32_t udpRxBuf1[8] = {0};
uint32_t udpRxBuf2[8] = {0};
uint32_t udpRxBuf3[8] = {0};
uint32_t udpRxBuf4[8] = {0};
uint8 udpRxCacheCnt = 0; //接收到来自APP的数据包计数

void ICACHE_FLASH_ATTR
udpclient_recv(void *arg, char *udpRx_buf, unsigned short len)
{
	uint32_t sndData[8] = {0};
	uint8 i = 0;

	for(i = 0; i < 8; i++) {
		sndData[i] = (((uint32_t)udpRx_buf[(i<<2)+3]) << 24) + (((uint32_t)udpRx_buf[(i<<2)+2]) << 16) + (((uint32_t)udpRx_buf[(i<<2)+1]) << 8) + (udpRx_buf[(i<<2)]);
	}
	if(sndData[0] == 0x020EEFCD) { //APP开启ESP8266 debugMode
		debugModeOpen = (udpRx_buf[4] == 0x00) ? 0x00 : 0x01;
		os_printf("debugMode=%d", debugModeOpen);
	}
	else {
		udpRxCacheCnt++;
		switch(udpRxCacheCnt) {
			case 1: SPISlaveSendData(SpiNum_HSPI, sndData, 8);
				break;
			case 2: memcpy(udpRxBuf0, sndData, 32);
				break;
			case 3: memcpy(udpRxBuf1, sndData, 32);
				break;
			case 4: memcpy(udpRxBuf2, sndData, 32);
				break;
			case 5: memcpy(udpRxBuf3, sndData, 32);
				break;
			case 6: memcpy(udpRxBuf4, sndData, 32);
				break;
			default:
				break;
		}
		if(debugModeOpen) {
			os_printf("udpRx(%d) ", udpRxCacheCnt);
		}
		// WRITE_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI), 0x8A);
		// WRITE_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI), 0x83);
		GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 1);//GPIO2输出高电平,通知STM32读取数据
	}
}
 /******************************************************************************
      * FunctionName : user_udp_sent_cb
      * Description  : udp sent successfully
      * Parameters  : arg -- Additional argument to pass to the callback function
      * Returns      : none
 *******************************************************************************/
LOCAL void ICACHE_FLASH_ATTR
user_udp_sent_cb(void *arg)
{
	struct espconn *pespconn = arg;
	udpTx_buf_pionter = udpTx_buf_pionter + UDP_LENGTH;
	
//	os_printf("-%d- ",udpWait2SendBytes);
	if(udpWait2SendBytes > UDP_LENGTH) {
		tst_udpCnt0++;
		udpWait2SendBytes = udpWait2SendBytes - UDP_LENGTH;
		espconn_send(&PhoneConn, udpTx_buf_pionter, UDP_LENGTH);
//		os_printf("x%d ",tst_udpCnt0);
	}
	else if(udpWait2SendBytes>0){
		tst_udpCnt1++;
		espconn_send(&PhoneConn, udpTx_buf_pionter, udpWait2SendBytes);
		udpWait2SendBytes = 0;
//		os_printf("u%d ",tst_udpCnt1);
	}
	
	//GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0);
	//os_printf("udp_send successfully !!!\n");
}
/******************************************************************************
 * FunctionName : user_devicefind_init
 * Description  : create a udp listening
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_udp_init(void)
{
	PhoneConn.type = ESPCONN_UDP;
	PhoneConn.proto.udp = &PhoneConnUdp;
	PhoneConn.proto.udp->local_port = 8888;//本地端口
	PhoneConn.proto.udp->remote_port = 8888;//远程端口
	PhoneConn.proto.udp->local_ip[0] = 255;
	PhoneConn.proto.udp->local_ip[1] = 255;
	PhoneConn.proto.udp->local_ip[2] = 255;
	PhoneConn.proto.udp->local_ip[3] = 255;

	PhoneConn.proto.udp->remote_ip[0] = 192;
	PhoneConn.proto.udp->remote_ip[1] = 168;
	PhoneConn.proto.udp->remote_ip[2] = 4;
	PhoneConn.proto.udp->remote_ip[3] = 2;

	espconn_regist_recvcb(&PhoneConn, udpclient_recv); // 注册一个UDP数据包接收回调
	espconn_regist_sentcb(&PhoneConn, user_udp_sent_cb); // register a udp packet sent callback
	espconn_create(&PhoneConn);//建立 UDP 传输
}
void ICACHE_FLASH_ATTR
my_user_udp_init(uint32 ipAddress)
{
	PhoneConn.type = ESPCONN_UDP;
	PhoneConn.proto.udp = &PhoneConnUdp;
	PhoneConn.proto.udp->local_port = 8888;//本地端口
	PhoneConn.proto.udp->remote_port = 8888;//远程端口
	PhoneConn.proto.udp->local_ip[0] = 255;
	PhoneConn.proto.udp->local_ip[1] = 255;
	PhoneConn.proto.udp->local_ip[2] = 255;
	PhoneConn.proto.udp->local_ip[3] = 255;

	PhoneConn.proto.udp->remote_ip[0] = (ipAddress>>24)&0x000000ff;
	PhoneConn.proto.udp->remote_ip[1] = (ipAddress>>16)&0x000000ff;
	PhoneConn.proto.udp->remote_ip[2] = (ipAddress>>8)&0x000000ff;
	PhoneConn.proto.udp->remote_ip[3] = (ipAddress)&0x000000ff;
	
	os_printf("%d.%d.%d.%d ",PhoneConn.proto.udp->remote_ip[0],PhoneConn.proto.udp->remote_ip[1],PhoneConn.proto.udp->remote_ip[2],PhoneConn.proto.udp->remote_ip[3]);
	espconn_regist_recvcb(&PhoneConn, udpclient_recv); // 注册一个UDP数据包接收回调
	espconn_regist_sentcb(&PhoneConn, user_udp_sent_cb); // register a udp packet sent callback
	espconn_create(&PhoneConn);//建立 UDP 传输
}


/******************************************************************************
 * FunctionName : user_set_softap_config
 * Description  : set SSID and password of ESP8266 softAP
 * Parameters   : none
 * Returns      : none
*******************************************************************************/
void ICACHE_FLASH_ATTR
user_set_softap_config(void)
{
	struct softap_config config;
	wifi_softap_get_config(&config); // Get config first.
    
	os_memset(config.ssid, 0, 32);
	os_memset(config.password, 0, 64);
	os_memcpy(config.ssid, "ESP8266", 7);
	os_memcpy(config.password, "12345678", 8);
	config.authmode = AUTH_WPA_WPA2_PSK;
	config.ssid_len = 0;// or its actual length
	config.max_connection = 4; // how many stations can connect to ESP8266 softAP at most.
 
	wifi_softap_set_config(&config);// Set ESP8266 softap config .
    
}
void ICACHE_FLASH_ATTR
my_set_softap_config()//my_set_softap_config(char *ssid, char *password)
{
	char ssid[15];
	char password[15];
	struct softap_config config;
 
	wifi_softap_get_config(&config); // Get config first.
    
	os_memset(config.ssid, 0, 32);
	os_memset(config.password, 0, 64);
	if(readFlash_SsidPassword() == 0) {
		os_memcpy(config.ssid, ssid_buf, strlen(ssid_buf));
		os_memcpy(config.password, password_buf, strlen(password_buf));
	}
	else {
		os_memcpy(config.ssid, "PREMAT3#null", 12);
		os_memcpy(config.password, "12345678", 8);
	}
	config.authmode = AUTH_WPA_WPA2_PSK;
	config.ssid_len = 0;// or its actual length
	config.max_connection = 4; // how many stations can connect to ESP8266 softAP at most.
 
	wifi_softap_set_config(&config);// Set ESP8266 softap config .
    
}
LOCAL void ICACHE_FLASH_ATTR
timer_callback(void *arg)
{
	if(udpDataNeedSend) {
		udpDataNeedSend = 0;
//		uart0_tx_buffer("T ", 2);
		udpTx_buf_pionter = udpTx_buf0;
		tst_udpCnt0 = 0;
		tst_udpCnt1 = 0;
		if(udpWait2SendBytes > UDP_LENGTH) {
			udpWait2SendBytes = udpWait2SendBytes - UDP_LENGTH;			
			espconn_send(&PhoneConn, udpTx_buf_pionter, UDP_LENGTH);
		}
		else {
			udpWait2SendBytes = 0;
			espconn_send(&PhoneConn, udpTx_buf_pionter, udpWait2SendBytes);	
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
}
/******************************************************************************
 * FunctionName : user_rf_cal_sector_set
 * Description  : SDK just reversed 4 sectors, used for rf init data and paramters.
 *                We add this function to force users to set rf cal sector, since
 *                we don't know which sector is free in user's application.
 *                sector map for last several sectors : ABCCC
 *                A : rf cal
 *                B : rf init data
 *                C : sdk parameters
 * Parameters   : none
 * Returns      : rf cal sector
*******************************************************************************/
uint32 ICACHE_FLASH_ATTR
user_rf_cal_sector_set(void)
{
    enum flash_size_map size_map = system_get_flash_size_map();
    uint32 rf_cal_sec = 0;

    switch (size_map) {
        case FLASH_SIZE_4M_MAP_256_256:
            rf_cal_sec = 128 - 5;
            break;

        case FLASH_SIZE_8M_MAP_512_512:
            rf_cal_sec = 256 - 5;
            break;

        case FLASH_SIZE_16M_MAP_512_512:
        case FLASH_SIZE_16M_MAP_1024_1024:
            rf_cal_sec = 512 - 5;
            break;

        case FLASH_SIZE_32M_MAP_512_512:
        case FLASH_SIZE_32M_MAP_1024_1024:
            rf_cal_sec = 1024 - 5;
            break;

        default:
            rf_cal_sec = 0;
            break;
    }

    return rf_cal_sec;
}

void ICACHE_FLASH_ATTR
user_rf_pre_init(void)
{
}


uint8 udpTx_busy = 0;
uint8 udpPackageId = 0;
uint8 udpFrameId = 0;
uint8 tst_Framecnt = 0;
uint16 udpBuf_index = 8;
uint16 udpBuf_frameHead = 0;
uint16 tstCnt=0;
uint16 udpSingleFrameFullCnt = 0;
 /******************************************************************************
      * FunctionName : user_udp_send
      * Description  : udp send data
      * Parameters  : none
      * Returns      : none
 *******************************************************************************/
 LOCAL void ICACHE_FLASH_ATTR
 user_udp_send(uint16 length)
 {
 //    char DeviceBuffer[40] = {0};
	char hwaddr[6];
	struct ip_info ipconfig;

	const char udp_remote_ip[4] = { 192, 168, 4, 2};  
	os_memcpy(PhoneConn.proto.udp->remote_ip, udp_remote_ip, 4); // ESP8266 udp remote IP need to be set everytime we call espconn_sent
	PhoneConn.proto.udp->remote_port = 8888;  // ESP8266 udp remote port need to be set everytime we call espconn_sent

	wifi_get_macaddr(STATION_IF, hwaddr);
	espconn_sendto(&PhoneConn, udpTx_buf0, length);//发送数据
    // espconn_sent(&user_udp_espconn, DeviceBuffer, os_strlen(DeviceBuffer));
     
 }
void  SPI_Receive_Data(void) {
	uint8 i = 0;
	uint32 recv_data0, recv_data;

	recv_data0 = READ_PERI_REG(SPI_W0(SpiNum_HSPI)); // 读取前4字节
	if((recv_data0 & 0x0000000f) == 0x0D) { //数据帧
		if(udpBuf_index>8296){
			if(debugModeOpen) {
				os_printf("Over4095\n\r",i,recv_data);
			}
		}
		udpPackageId = (recv_data0 >> 8) & 0x000000ff;
		udpTx_buf0[udpBuf_index++] = (recv_data0>>16) & 0xff;
		udpTx_buf0[udpBuf_index++] = (recv_data0>>24) & 0xff;
		tstCnt = tstCnt + 2;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			udpTx_buf0[udpBuf_index++] = recv_data & 0xff;
			udpTx_buf0[udpBuf_index++] = (recv_data>>8) & 0xff;
			udpTx_buf0[udpBuf_index++] = (recv_data>>16) & 0xff;
			udpTx_buf0[udpBuf_index++] = (recv_data>>24) & 0xff;
			tstCnt = tstCnt + 4;
		}
		udpSingleFrameFullCnt = udpSingleFrameFullCnt + 30;
		if(recv_data0 & 0x000000f0) { // 尾帧判断
			udpBuf_index = udpBuf_index - (30-(((recv_data0>>4)&0x0000000f)<<1));//修正有效长度
			udpTx_buf0[udpBuf_frameHead] = 0x0D;
			udpTx_buf0[udpBuf_frameHead+1] = udpPackageId; // 包ID
			udpTx_buf0[udpBuf_frameHead+2] = udpFrameId; // 帧ID
			udpTx_buf0[udpBuf_frameHead+3] = 0x01; // 尾帧标识
			udpTx_buf0[udpBuf_frameHead+4] = ((udpBuf_index-udpBuf_frameHead)>>8)&0x00ff; // 有效数据长度
			udpTx_buf0[udpBuf_frameHead+5] = (udpBuf_index-udpBuf_frameHead)&0x00ff; // 有效数据长度 1400
			tst_Framecnt++;
//			os_printf("E%d,%d ",udpBuf_index,(udpBuf_index-udpBuf_frameHead));
			if(debugModeOpen) {
				os_printf("slv_d");
			}
			udpDataNeedSend = 1;
			udpWait2SendBytes = udpBuf_index;
			udpBuf_index = 8;
			udpBuf_frameHead = 0;
			udpFrameId = 0;
			udpSingleFrameFullCnt = 0;
			tstCnt = 0;
		}
		else if(udpSingleFrameFullCnt == 1440) {
			udpTx_buf0[udpBuf_frameHead] = 0x0D;
			udpTx_buf0[udpBuf_frameHead+1] = udpPackageId; // 包ID
			udpTx_buf0[udpBuf_frameHead+2] = udpFrameId++; // 帧ID
			udpTx_buf0[udpBuf_frameHead+3] = 0x00; // 尾帧标识
			udpTx_buf0[udpBuf_frameHead+4] = 0x05; // 有效数据长度
			udpTx_buf0[udpBuf_frameHead+5] = 0xA0; // 有效数据长度 1440
			udpBuf_index = udpBuf_index + 8;
			udpBuf_frameHead = udpBuf_frameHead + UDP_LENGTH;
			udpSingleFrameFullCnt = 0;
		}
//		os_printf("send to App Data");
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0A) { // 发送给 APP 的指令
		uint8 idex = 0;
		for(i = 0; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			udpTxCmd_buf[idex++] = recv_data & 0xff;
			udpTxCmd_buf[idex++] = (recv_data>>8) & 0xff;
			udpTxCmd_buf[idex++] = (recv_data>>16) & 0xff;
			udpTxCmd_buf[idex++] = (recv_data>>24) & 0xff;
		}
		espconn_send(&PhoneConn, udpTxCmd_buf, idex);//发送数据
		if(debugModeOpen) {
			os_printf("send to App ");
		}
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
	}
	else if((recv_data0 & 0x000000ff) == 0x0E) { // 发送给 ESP8266 的指令
		uint8 index = 0;
		uint8 j = 0;
		tst_Framecnt = 0;
		udpRx_buf[index++] = (recv_data0>>16) & 0xff;
		udpRx_buf[index++] = (recv_data0>>24) & 0xff;
		for(i = 1; i < 8; i++) {
			recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
			udpRx_buf[index++] = recv_data & 0xff;
			udpRx_buf[index++] = (recv_data>>8) & 0xff;
			udpRx_buf[index++] = (recv_data>>16) & 0xff;
			udpRx_buf[index++] = (recv_data>>24) & 0xff;
		}
		if(((recv_data0>>8) & 0x000000ff) == 0x05) { // 修改 ssid 和 密码
			char str1[15], str2[15];
			// udpRx_buf[index++] = (recv_data0>>16) & 0xff;
			// udpRx_buf[index++] = (recv_data0>>24) & 0xff;
			// for(i = 1; i < 8; i++) {
				// recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
				// udpRx_buf[index++] = recv_data & 0xff;
				// udpRx_buf[index++] = (recv_data>>8) & 0xff;
				// udpRx_buf[index++] = (recv_data>>16) & 0xff;
				// udpRx_buf[index++] = (recv_data>>24) & 0xff;
			// }
			for( i = 0; i < 15; i++) {
				ssid_buf[i] = udpRx_buf[j++];
			}
			for( i = 0; i < 15; i++) {
				 password_buf[i] = udpRx_buf[j++];
			}
			saveData2Flash();
//			my_set_softap_config(str1, str2);
//			 os_printf("%s,%s",str1,str2);
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x06) { // 修改 UDP 远端 ip 地址
			uint8 index = 0;
			uint32 ipAddress;
			// udpRx_buf[index++] = (recv_data0>>16) & 0xff;
			// udpRx_buf[index++] = (recv_data0>>24) & 0xff;
			// for(i = 1; i < 8; i++) {
				// recv_data = READ_PERI_REG(SPI_W0(SpiNum_HSPI)+(i<<2));
				// udpRx_buf[index++] = recv_data & 0xff;
				// udpRx_buf[index++] = (recv_data>>8) & 0xff;
				// udpRx_buf[index++] = (recv_data>>16) & 0xff;
				// udpRx_buf[index++] = (recv_data>>24) & 0xff;
			// }
			ipAddress = (udpRx_buf[0]<<24) | (udpRx_buf[1]<<16) | (udpRx_buf[2]<<8) | udpRx_buf[3];
			if(debugModeOpen) {
				os_printf(" from STM32 ipAddress %d",ipAddress);
			}
//			os_printf(" form STM32 ipAddress %d",ipAddress);
			my_user_udp_init(ipAddress);
		}
		else if(((recv_data0>>8) & 0x000000ff) == 0x02) { // 开启调试串口打印
			debugModeOpen = (udpRx_buf[4] == 0x00) ? 0x00 : 0x01;
			os_printf("debugMode=%d, ESP8266 firmware version=%d", debugModeOpen, version);
		}

	}
	//return 0;
}

// SPI interrupt callback function.
void spi_slave_isr_sta(void *para)
{
    uint32 regvalue;
    uint32 statusW, statusR, counter;
	uint32 recv_data[16]; 
	
    if (READ_PERI_REG(0x3ff00020)&BIT4) {
        //following 3 lines is to clear isr signal
        CLEAR_PERI_REG_MASK(SPI_SLAVE(SpiNum_SPI), 0x3ff);
    } else if (READ_PERI_REG(0x3ff00020)&BIT7) { //bit7 is for hspi isr,
		GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 1);
        regvalue = READ_PERI_REG(SPI_SLAVE(SpiNum_HSPI));
//        os_printf("spi_slave_isr_sta SPI_SLAVE[0x%08x]\n\r", regvalue);
        SPIIntClear(SpiNum_HSPI);
        SET_PERI_REG_MASK(SPI_SLAVE(SpiNum_HSPI), SPI_SYNC_RESET);
        SPIIntClear(SpiNum_HSPI);
        
        SPIIntEnable(SpiNum_HSPI, SpiIntSrc_WrStaDone
                 | SpiIntSrc_RdStaDone 
                 | SpiIntSrc_WrBufDone 
                 | SpiIntSrc_RdBufDone);

        if (regvalue & SPI_SLV_WR_BUF_DONE) {
			char i = 0;			
            // User can get data from the W0~W7
//           os_printf("spi_slave_isr_sta : SPI_SLV_WR_BUF_DONE\n\r");
			SPI_Receive_Data();
        }
		else if (regvalue & SPI_SLV_RD_BUF_DONE) {
            // TO DO 
//            os_printf("spi_slave_isr_sta : SPI_SLV_RD_BUF_DONE\n\r"); 
			if(udpRxCacheCnt > 0) {
				udpRxCacheCnt--;
			}
			else {
				udpRxCacheCnt = 0;
			}
			if(debugModeOpen) {
				os_printf("slv_r(%d) ", udpRxCacheCnt);
			}
			if(udpRxCacheCnt > 0) {
				SPISlaveSendData(SpiNum_HSPI, udpRxBuf0, 8);
				
				switch(udpRxCacheCnt) {
					case 1: 
						break;
					case 2: memcpy(udpRxBuf0, udpRxBuf1, 32);
						break;
					case 3: memcpy(udpRxBuf0, udpRxBuf1, 32);
						memcpy(udpRxBuf1, udpRxBuf2, 32);
						break;
					case 4: memcpy(udpRxBuf0, udpRxBuf1, 32);
						memcpy(udpRxBuf1, udpRxBuf2, 32);
						memcpy(udpRxBuf2, udpRxBuf3, 32);
						break;
					case 5: memcpy(udpRxBuf0, udpRxBuf1, 32);
						memcpy(udpRxBuf1, udpRxBuf2, 32);
						memcpy(udpRxBuf2, udpRxBuf3, 32);
						memcpy(udpRxBuf3, udpRxBuf4, 32);
						break;
					default:
						if(debugModeOpen) {
							os_printf("slv_r_over(%d) ", udpRxCacheCnt);
						}
						break;
				}
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 1);// GPIO2 输出高，数据读取完毕
			}
			else {
				GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0);// GPIO2 输出低，数据读取完毕
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
			
        }
        if (regvalue & SPI_SLV_RD_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_RD_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }

        if (regvalue & SPI_SLV_WR_STA_DONE) {
            statusR = READ_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI));
            statusW = READ_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI));
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_SLV_WR_STA_DONE[R=0x%08x,W=0x%08x]\n\r", statusR, statusW);
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        if ((regvalue & SPI_TRANS_DONE) && ((regvalue & 0xf) == 0)) {
			if(debugModeOpen) {
				os_printf("spi_slave_isr_sta : SPI_TRANS_DONE\n\r");
			}
			GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // HSPI 可以接收新数据
        }
        //SHOWSPIREG(SpiNum_HSPI);
    }
	
}

// Test spi slave interfaces.
void ICACHE_FLASH_ATTR spi_slave_init()
{
    //
    SpiAttr hSpiAttr;
    hSpiAttr.bitOrder = SpiBitOrder_MSBFirst;
    hSpiAttr.speed = 0;
    hSpiAttr.mode = SpiMode_Slave;
    hSpiAttr.subMode = SpiSubMode_0;

    // Init HSPI GPIO
    WRITE_PERI_REG(PERIPHS_IO_MUX, 0x105);
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDI_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTCK_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTMS_U, 2);//configure io to spi mode
    PIN_FUNC_SELECT(PERIPHS_IO_MUX_MTDO_U, 2);//configure io to spi mode

    os_printf("\r\n == spi init slave ==\r\n");
    SPIInit(SpiNum_HSPI, &hSpiAttr);
    
    // Set spi interrupt information.
    SpiIntInfo spiInt;
    spiInt.src = (SpiIntSrc_TransDone 
        | SpiIntSrc_WrStaDone 
        |SpiIntSrc_RdStaDone 
        |SpiIntSrc_WrBufDone 
        |SpiIntSrc_RdBufDone);
    spiInt.isrFunc = spi_slave_isr_sta;
    SPIIntCfg(SpiNum_HSPI, &spiInt);
   // SHOWSPIREG(SpiNum_HSPI);
    
    SPISlaveRecvData(SpiNum_HSPI);
    // uint32_t sndData[8] = { 0 };
    // sndData[0] = 0x35343332;
    // sndData[1] = 0x39383736;
    // sndData[2] = 0x3d3c3b3a;
    // sndData[3] = 0x11103f3e;
    // sndData[4] = 0x15141312;
    // sndData[5] = 0x19181716;
    // sndData[6] = 0x1d1c1b1a;
    // sndData[7] = 0x21201f1e;

//    SPISlaveSendData(SpiNum_HSPI, sndData, 8);
//    WRITE_PERI_REG(SPI_RD_STATUS(SpiNum_HSPI), 0x8A);
//    WRITE_PERI_REG(SPI_WR_STATUS(SpiNum_HSPI), 0x83);
}

/**
 * @brief Test spi interfaces.
 *
 */
void ICACHE_FLASH_ATTR
user_init(void)
{
	int i = 0;
	uart_init(BIT_RATE_115200,BIT_RATE_115200);
//	saveData2Flash();
	wifi_set_opmode(2);//STATIONAP_MODE); //(0x01:Station、0x02:SoftAP、0x03:Station+SoftAP)
    // ESP8266 softAP set config.
 //   user_set_softap_config();
	my_set_softap_config();
	//disarm timer first
	os_timer_disarm(&test_timer); //关闭定时器，相当于清零计时器计数
	//re-arm timer to check ip
	os_timer_setfn(&test_timer, (os_timer_func_t *)timer_callback, NULL); //初始化定时器// only send next packet after prev packet sent successfully
	os_timer_arm(&test_timer, 5, 1);
	
//	espconn_init();
   // Create udp listening.
	user_udp_init();
//	os_delay_us(1000000);
	wifi_station_set_reconnect_policy(false);
	wifi_station_set_auto_connect(0);
	wifi_set_sleep_type(NONE_SLEEP_T);
	system_soft_wdt_stop ();
	// IO2 配置为普通输出IO，通知STM32读取数据，0无新数据,1有新数据
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO2_U, FUNC_GPIO0);
	// IO4 配置为普通输出IO，通知STM32当前Busy, 0空闲, 1忙
	PIN_FUNC_SELECT(PERIPHS_IO_MUX_GPIO4_U, FUNC_GPIO0);
	// IO2 IO4 初始化
	GPIO_OUTPUT_SET(GPIO_ID_PIN(2), 0); // IO2 置低
	GPIO_OUTPUT_SET(GPIO_ID_PIN(4), 0); // IO2 置低
//	os_printf("\r\n system_deep_sleep \r\n");
	//system_deep_sleep(0);
	//	spi_interface_test();
	spi_slave_init();
	
}

